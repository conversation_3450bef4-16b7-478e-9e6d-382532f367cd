<x-modal-dialog :show-variable="'showDialog'" :max-width="'2xl'" :close-method="'closeDialog()'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900" x-text="dialogTitle"></h3>
        <button @click="closeDialog()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    <form @submit.prevent="submitForm()">
        <div class="space-y-4">
            <!-- Note: user_id is now handled through donations table -->
            <template x-for="(link, index) in formData.social_media" :key="index">
                <input type="hidden" :name="'social_media['+index+']'" x-model="formData.social_media[index]">
            </template>
            
            <div>
                <label for="donatur-name" class="block text-sm text-gray-500 mb-1">Name <span class="text-red-500">*</span></label>
                <input 
                    type="text"
                    id="donatur-name"
                    x-model="formData.name"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.name}"
                >
                <div x-show="errors.name" x-text="errors.name" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <div>
                <label for="phone" class="block text-sm text-gray-500 mb-1">Phone</label>
                <x-phone-input
                    id="phone"
                    name="phone"
                    x-model="formData.phone"
                    placeholder="+62 8xx xxxx xxxx"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.phone}"
                />
                <div x-show="errors.phone" x-text="errors.phone" class="mt-1 text-sm text-red-500"></div>
            </div>

            <!-- Penghubung field -->
            <div x-data="{
                init() {
                    this.$nextTick(() => {
                        if (formData.user_id) {
                            this.fetchAndSetUser(formData.user_id);
                        }
                    });
                    
                    this.$el.addEventListener('user-selected', (event) => {
                        formData.user_id = event.detail.user.id;
                        selectedUser = event.detail.user;
                    });
                    
                    this.$el.addEventListener('user-cleared', () => {
                        formData.user_id = null;
                        selectedUser = null;
                        
                        // Also clear the penghubung form component
                        if (this.$refs.penghubungForm) {
                            this.$refs.penghubungForm.dispatchEvent(new CustomEvent('user-cleared'));
                        }
                    });
                },
                
                async fetchAndSetUser(userId) {
                    if (!userId) return;
                    
                    try {
                        const response = await fetch(`/donatur/get-user/${userId}`);
                        
                        if (response.ok) {
                            const user = await response.json();
                            selectedUser = user;
                            
                            this.$refs.penghubungForm.dispatchEvent(
                                new CustomEvent('set-selected-user', { 
                                    detail: { user: user } 
                                })
                            );
                        }
                    } catch (error) {
                        console.error('Error fetching user:', error);
                    }
                }
            }">
                <label for="penghubung-input" class="block text-sm text-gray-500 mb-1">Penghubung</label>
                <div class="flex items-center">
                    <x-penghubung-form 
                        x-ref="penghubungForm"
                        id="penghubung-input"
                        :placeholder="'Search for penghubung...'"
                        class="flex-1"
                    />
                    <!-- Removed the duplicate X button that was here -->
                </div>
                <div x-show="errors.user_id" x-text="errors.user_id" class="mt-1 text-sm text-red-500"></div>
            </div>
            
            <div>
                <label for="donatur-description" class="block text-sm text-gray-500 mb-1">Description</label>
                <textarea 
                    id="donatur-description"
                    x-model="formData.description"
                    rows="3"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.description}"
                ></textarea>
                <div x-show="errors.description" x-text="errors.description" class="mt-1 text-sm text-red-500"></div>
            </div>

            <div>
                <label for="donatur-address" class="block text-sm text-gray-500 mb-1">Address</label>
                <textarea 
                    id="donatur-address"
                    x-model="formData.address"
                    rows="3"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.address}"
                ></textarea>
                <div x-show="errors.address" x-text="errors.address" class="mt-1 text-sm text-red-500"></div>
            </div>

            <div>
                <label for="donatur-email" class="block text-sm text-gray-500 mb-1">Email</label>
                <input 
                    type="email"
                    id="donatur-email"
                    x-model="formData.email"
                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                    x-bind:class="{'border-red-300 focus:ring-red-500': errors.email}"
                >
                <div x-show="errors.email" x-text="errors.email" class="mt-1 text-sm text-red-500"></div>
            </div>

            {{-- Social Media Links --}}
            <div>
                <label class="block text-sm text-gray-500 mb-1">Social Media Links</label>
                <div class="space-y-2">
                    <template x-for="(link, index) in socialMediaLinks" :key="index">
                        <div class="flex items-center space-x-2">
                            <input 
                                type="text" 
                                x-model="socialMediaLinks[index]" 
                                placeholder="https://example.com/profile"
                                class="block flex-1 px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                            >
                            <button type="button" @click="socialMediaLinks.splice(index, 1)" class="p-2 text-gray-400 hover:text-gray-600">
                                <x-icon name="trash" width="16" height="16" />
                            </button>
                        </div>
                    </template>
                    
                    <button type="button" @click="socialMediaLinks.push('')" class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <x-icon name="plus" width="16" height="16" class="mr-2" />
                        Add Social Media Link
                    </button>
                </div>
                <div x-show="errors.social_media" x-text="errors.social_media" class="mt-1 text-sm text-red-500"></div>
            </div>
        </div>
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100 mt-4">
            <button type="button" @click="submitForm()" class="flex px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150" x-bind:disabled="loading">
                <span x-show="!loading">Save</span>
                <span x-show="loading">
                    <x-icon name="spinner" width="16" height="16" class="animate-spin text-white my-1" />
                </span>
            </button>
        </div>
    </form>
</x-modal-dialog>
