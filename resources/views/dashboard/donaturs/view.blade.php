<x-modal-dialog :show-variable="'showViewDialog'" :max-width="'lg'" :close-method="'showViewDialog = false'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900">Donatur Details</h3>
        <button @click="showViewDialog = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    
    <div class="space-y-4">
        <div>
            <label class="block text-sm text-gray-500 mb-1">Name</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.name || '-'"></p>
        </div>
        
        <div>
            <label class="block text-sm text-gray-500 mb-1">Phone</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="formatPhoneForDisplay(viewData.phone) || '-'"></p>
        </div>

        <div>
            <label class="block text-sm text-gray-500 mb-1">Manager</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.user && viewData.user.manager ? viewData.user.manager : '-'"></p>
        </div>

        <div>
            <label class="block text-sm text-gray-500 mb-1">Penghubung</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.user ? viewData.user.name : '-'"></p>
        </div>

        <div>
            <label class="block text-sm text-gray-500 mb-1">Description</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.description || '-'"></p>
        </div>

        <div>
            <label class="block text-sm text-gray-500 mb-1">Address</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.address || '-'"></p>
        </div>

        <div>
            <label class="block text-sm text-gray-500 mb-1">Email</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.email || '-'"></p>
        </div>
        
        <div>
            <label class="block text-sm text-gray-500 mb-1">Social Media</label>
            <template x-if="viewData.social_media && viewData.social_media.length > 0">
                <div class="space-y-2">
                    <template x-for="(link, index) in viewData.social_media" :key="index">
                        <a :href="link" target="_blank" class="block w-full px-3 py-2 rounded-md bg-gray-50 text-blue-500 hover:text-blue-700 truncate" x-text="link"></a>
                    </template>
                </div>
            </template>
            <template x-if="!viewData.social_media || viewData.social_media.length === 0">
                <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-500">-</p>
            </template>
        </div>
    </div>
    
    <div class="mt-6 flex justify-end space-x-3">
        <button type="button" @click="showViewDialog = false" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 shadow-sm transition duration-150">
            Close
        </button>
        @if(auth()->user()->isAdmin())
        <button type="button" @click="showViewDialog = false; $nextTick(() => editDonatur(viewData.id))" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150">
            Edit
        </button>
        @endif
    </div>
</x-modal-dialog>
