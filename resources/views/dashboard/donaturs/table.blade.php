
<div class="overflow-auto bg-white rounded-lg shadow">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manager</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Penghubung</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            @forelse ($donaturs as $donatur)
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ $donatur->name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <x-phone-display :phone="$donatur->phone" />
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ $donatur->users->first()->manager ?? '-' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ $donatur->users->first()->name ?? '-' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <button @click="$dispatch('view-donatur', { id: '{{ $donatur->id }}' })" class="text-blue-600 hover:text-blue-900">
                                <x-icon name="eye" width="18" height="18" />
                            </button>
                            <button @click="$dispatch('edit-donatur', { id: '{{ $donatur->id }}' })" class="text-indigo-600 hover:text-indigo-900">
                                <x-icon name="pencil" width="18" height="18" />
                            </button>
                            @if(auth()->user()->isAdmin())
                            <button @click="confirmDelete('{{ $donatur->id }}')" class="text-red-600 hover:text-red-900">
                                <x-icon name="trash" width="18" height="18" />
                            </button>
                            @endif
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                        No donaturs found
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<!-- Pagination -->
<div class="mt-1 px-4 py-3 rounded-b-lg" id="pagination-links">
    <x-pagination :paginator="$donaturs" />
</div>
