<x-modal-dialog :show-variable="'showViewDialog'" :max-width="'lg'" :close-method="'showViewDialog = false'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900">Donation Details</h3>
        <button @click="showViewDialog = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    <!-- (Optional) Image preview for donation proof (if available) -->
    <div x-show="viewData.image_url" class="mb-4">
        <img :src="viewData.image_url" class="w-full h-48 object-cover rounded-md" alt="Donation Proof">
    </div>
    <div class="space-y-4">
        <div>
            <label class="block text-sm text-gray-500 mb-1">Donor</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.donor_name"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Amount</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.amount ? 'IDR ' + new Intl.NumberFormat('id-ID').format(viewData.amount) : '-' "></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Program</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.program_name"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Status</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900 capitalize"
               x-bind:class="{
                   'text-blue-700': viewData.status === 'pending',
                   'text-green-700': viewData.status === 'success',
                   'text-red-700': viewData.status === 'failed',
                   'text-gray-700': viewData.status === 'draft'
               }"
               x-text="viewData.status === 'pending' ? 'Pending' : (viewData.status === 'success' ? 'Success' : (viewData.status === 'failed' ? 'Failed' : 'Draft'))"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Date</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.created_at ? formatDateIndonesian(viewData.created_at) : '-' "></p>
        </div>
    </div>
    <div class="flex justify-end mt-4">
        <button @click="showViewDialog = false" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">Close</button>
    </div>
</x-modal-dialog>
