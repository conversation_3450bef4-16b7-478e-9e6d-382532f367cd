
@extends('layouts.app')

@section('title', 'Users Management')
@section('panel-type', 'User Panel')

@section('content')
<div x-data="usersManagement()"
     x-cloak
     @view-user.window="viewUser($event.detail.id)"
     @edit-user.window="editUser($event.detail.id)">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Users Management</h2>
        <button @click="openCreateDialog()" class="bg-blue-500 text-white text-sm px-4 py-2 rounded-lg hover:bg-blue-600 cursor-pointer">
            New
        </button>
    </div>

    <!-- Filter Form -->
    @include('dashboard.users.filter')

    <!-- Users Table with Loading Overlay -->
    <div class="relative">
        <div x-show="loading"
             class="absolute inset-0 backdrop-blur-xs flex items-center justify-center z-10">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span class="text-gray-500 text-sm">Loading...</span>
            </div>
        </div>
        <div id="users-table" x-ref="usersTable">
            @include('dashboard.users.table')
        </div>
    </div>

    <!-- User Form Dialog -->
    @include('dashboard.users.form')

    <!-- View User Dialog -->
    @include('dashboard.users.view')
</div>
@endsection

<script>
function usersManagement() {
    return {
        showDialog: false,
        showViewDialog: false,
        dialogTitle: '',
        loading: false,
        currentPageUrl: '',
        formData: {
            id: null,
            name: '',
            email: '',
            phone: '',
            role: 'user',
            manager: ''
        },
        viewData: {},
        filters: {
            name: '',
            role: '',
            manager: ''
        },
        errors: {},
        
        init() {
            this.setupPagination();
            this.currentPageUrl = window.location.pathname + window.location.search;
            
            // Initialize filters from URL parameters if they exist
            const urlParams = new URLSearchParams(window.location.search);
            this.filters.name = urlParams.get('name') || '';
            this.filters.role = urlParams.get('role') || '';
            this.filters.manager = urlParams.get('manager') || '';
        },

        setupPagination() {
            document.addEventListener('click', (e) => {
                const element = e.target.closest('#pagination-links a');
                if (element) {
                    e.preventDefault();
                    this.loadPage(element.href);
                }
            });
        },

        async loadPage(url) {
            try {
                this.loading = true;
                // Store the current page URL
                this.currentPageUrl = url;

                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const html = await response.text();

                if (!document.startViewTransition) {
                    this.$refs.usersTable.innerHTML = html;
                    Alpine.initTree(this.$refs.usersTable);
                    return;
                }

                document.startViewTransition(() => {
                    this.$refs.usersTable.innerHTML = html;
                    Alpine.initTree(this.$refs.usersTable);
                });
            } catch (error) {
                console.error('Error loading page:', error);
            } finally {
                this.loading = false;
            }
        },

        applyFilters() {
            const url = new URL(window.location.pathname, window.location.origin);
            
            // Clear existing query parameters
            url.searchParams.delete('name');
            url.searchParams.delete('manager');
            url.searchParams.delete('role');
            
            // Add new filter parameters if they have values
            if (this.filters.name) url.searchParams.set('name', this.filters.name);
            if (this.filters.manager) url.searchParams.set('manager', this.filters.manager);
            if (this.filters.role) url.searchParams.set('role', this.filters.role);
            
            this.loadPage(url.toString());
        },

        async viewUser(id) {
            try {
                this.loading = true;
                
                const response = await fetch(`/settings/user/${id}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch user data');
                }
                
                this.viewData = await response.json();
                
                // Set the dialog to show
                this.showViewDialog = true;
            } catch (error) {
                console.error('Error in viewUser:', error);
                alert('Failed to load user details: ' + error.message);
            } finally {
                this.loading = false;
            }
        },

        closeViewDialog() {
            this.showViewDialog = false;
        },

        async editUser(id) {
            try {
                this.loading = true;
                
                const response = await fetch(`/settings/user/${id}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch user data');
                }
                
                const userData = await response.json();
                
                // Set form data
                this.formData = {
                    id: userData.id,
                    name: userData.name,
                    email: userData.email,
                    role: userData.role,
                    phone: userData.phone || '',
                    manager: userData.manager || ''
                };
                
                this.dialogTitle = 'Edit User';
                this.errors = {};
                this.showDialog = true;
            } catch (error) {
                console.error('Error in editUser:', error);
                alert('Failed to load user details: ' + error.message);
            } finally {
                this.loading = false;
            }
        },

        openCreateDialog() {
            this.dialogTitle = 'New User';
            this.formData = {
                id: null,
                name: '',
                email: '',
                role: 'user',
                phone: '',
                manager: ''
            };
            this.errors = {};
            this.showDialog = true;
        },

        closeDialog() {
            this.showDialog = false;
        },

        handleRoleChange() {
            // If role is not staff or admin, clear manager
            if (!['staff', 'admin'].includes(this.formData.role)) {
                this.formData.manager = '';
            }
        },

        validateForm() {
            this.errors = {};
            
            // Basic validation
            if (!this.formData.name) {
                this.errors.name = 'Name is required';
            }
            
            if (!this.formData.email) {
                this.errors.email = 'Email is required';
            } else if (!/\S+@\S+\.\S+/.test(this.formData.email)) {
                this.errors.email = 'Email format is invalid';
            }
            
            // Phone validation - only check format if provided
            if (this.formData.phone && this.formData.phone.trim()) {
                // The phone input component should always format it correctly,
                // but just in case, check if it starts with +62
                if (!this.formData.phone.trim().startsWith('+62')) {
                    this.errors.phone = 'Phone number must start with +62';
                }
            }
            
            // Role validation
            if (!this.formData.role) {
                this.errors.role = 'Role is required';
            }
            
            // Manager validation for staff and admin roles
            if ((this.formData.role === 'staff' || this.formData.role === 'admin') && !this.formData.manager) {
                this.errors.manager = 'Manager is required for staff and admin roles';
            }
            
            return Object.keys(this.errors).length === 0;
        },

        async submitForm() {
            if (!this.validateForm()) {
                return;
            }
            
            try {
                this.loading = true;
                this.errors = {};
                
                let url = '/settings/user';
                let method = 'POST';
                
                if (this.formData.id) {
                    url = `/settings/user/${this.formData.id}`;
                    method = 'PUT';
                }
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(this.formData)
                });
                
                // Check if the response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Server returned an invalid response. Please try again later.');
                }
                
                const result = await response.json();
                
                if (!response.ok) {
                    if (response.status === 422) {
                        this.errors = result.errors || {};
                    } else {
                        throw new Error(result.message || 'An error occurred');
                    }
                } else {
                    this.closeDialog();
                    this.loadPage(this.currentPageUrl);
                }
            } catch (error) {
                alert('An error occurred while saving the user: ' + error.message);
            } finally {
                this.loading = false;
            }
        },

        formatPhoneForDisplay(phone) {
            if (!phone) return '-';

            // Remove all non-numeric characters and '+' sign
            let number = phone.replace(/[^0-9]/g, '');

            // If it starts with '62', remove it as we'll add it back
            if (number.startsWith('62')) {
                number = number.substring(2);
            }

            // Format as: +62 8xx xxxx xxxx
            return '+62 ' + number.substring(0, 3) + ' ' +
                   number.substring(3, 7) + ' ' +
                   number.substring(7);
        }
    };
}
</script>
