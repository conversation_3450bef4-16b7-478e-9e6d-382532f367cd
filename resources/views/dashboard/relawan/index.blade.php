@extends('layouts.app')

@section('title', 'Relawan Management')

@section('content')
<div x-data="relawanManagement()"
     x-cloak
     @view-relawan.window="viewRelawan($event.detail.id)"
     @edit-relawan.window="editRelawan($event.detail.id)"
     @delete-relawan.window="confirmDelete($event.detail.id)">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Relawan Management</h2>
        @if(auth()->user()->isAdmin())
        <button @click="openCreateDialog()" class="bg-blue-500 text-white text-sm px-4 py-2 rounded-lg hover:bg-blue-600 cursor-pointer">
            New
        </button>
        @endif
    </div>

    <!-- Filters -->
    @include('dashboard.relawan.filter')

    <!-- Relawan Table with Loading Overlay -->
    <div class="relative">
        <div x-show="loading"
             class="absolute inset-0 backdrop-blur-xs flex items-center justify-center z-10">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span class="text-gray-500 text-sm">Loading...</span>
            </div>
        </div>
        <div id="relawan-table" x-ref="relawanTable">
            @include('dashboard.relawan.table')
        </div>
    </div>

    <!-- Create/Edit Dialog -->
    @include('dashboard.relawan.form')

    <!-- View Dialog -->
    @include('dashboard.relawan.view')

    <!-- Delete Confirmation Modal -->
    <x-delete-confirmation 
        showVariable="showDeleteDialog"
        title="Confirm Deletion"
        message="Are you sure you want to delete this relawan? This action cannot be undone."
        deleteMethod="deleteRelawan()"
        cancelMethod="showDeleteDialog = false"
    />
</div>

<script>
function relawanManagement() {
    return {
        loading: false,
        showDialog: false,
        showViewDialog: false,
        showDeleteDialog: false,
        dialogMode: 'create',
        currentRelawanId: null,
        currentPageUrl: '',
        selectedFile: null,
        relawan: {
            name: '',
            email: '',
            phone: '',
            address: '',
            description: '',
            program_ids: [],
            social_media: [],
            image_url: null,
            image_path: null
        },
        filters: {
            name: '',
            program: ''
        },
        errors: {},
        programSearchTerm: '',
        programLoading: false,
        filteredPrograms: [],
        showProgramResults: false,
        programSearchTimeout: null,
        
        init() {
            this.setupPagination();
            this.currentPageUrl = window.location.pathname + window.location.search;
            
            // Initialize filters from URL parameters if they exist
            const urlParams = new URLSearchParams(window.location.search);
            this.filters.name = urlParams.get('name') || '';
            this.filters.program = urlParams.get('program') || '';
            
            // Make sure relawan.programs is initialized as an array
            if (!this.relawan.programs) {
                this.relawan.programs = [];
            }
            
            // Close program dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('#program-search')) {
                    this.showProgramResults = false;
                }
            });
            
            window.addEventListener('close-all-dialogs', () => {
                this.closeDialog();
                this.showViewDialog = false;
                this.showDeleteDialog = false;
            });
        },
        
        setupPagination() {
            document.addEventListener('click', (e) => {
                const element = e.target.closest('#pagination-links a');
                if (element) {
                    e.preventDefault();
                    this.loadPage(element.href);
                }
            });
        },

        async loadPage(url) {
            try {
                this.loading = true;
                // Store the current page URL
                this.currentPageUrl = url;

                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const html = await response.text();
                
                // Create a temporary div to parse the HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                // Find the table content in the response
                const tableContent = tempDiv.querySelector('#relawan-table-content');
                
                if (!tableContent) {
                    console.error('Could not find table content in response');
                    return;
                }

                if (!document.startViewTransition) {
                    // Update only the table content, not the entire table
                    const tableContentContainer = this.$refs.relawanTable.querySelector('#relawan-table-content');
                    if (tableContentContainer) {
                        tableContentContainer.innerHTML = tableContent.innerHTML;
                        Alpine.initTree(tableContentContainer);
                    }
                    return;
                }

                document.startViewTransition(() => {
                    // Update only the table content, not the entire table
                    const tableContentContainer = this.$refs.relawanTable.querySelector('#relawan-table-content');
                    if (tableContentContainer) {
                        tableContentContainer.innerHTML = tableContent.innerHTML;
                        Alpine.initTree(tableContentContainer);
                    }
                });
            } catch (error) {
                console.error('Error loading page:', error);
            } finally {
                this.loading = false;
            }
        },

        applyFilters() {
            const url = new URL(window.location.pathname, window.location.origin);
            
            // Clear existing query parameters
            url.searchParams.delete('name');
            url.searchParams.delete('program');
            
            // Add new filter parameters if they have values
            if (this.filters.name) url.searchParams.set('name', this.filters.name);
            if (this.filters.program) url.searchParams.set('program', this.filters.program);
            
            this.loadPage(url.toString());
        },
        
        openCreateDialog() {
            this.dialogMode = 'create';
            this.resetForm();
            this.showDialog = true;
        },
        
        closeDialog() {
            this.showDialog = false;
            this.errors = {};
            this.selectedFile = null;
            
            // Reset file input if it exists
            if (this.$refs.fileInput) {
                this.$refs.fileInput.value = '';
            }
        },
        
        resetForm() {
            this.relawan = {
                name: '',
                email: '',
                phone: '',
                address: '',
                description: '',
                program_ids: [],
                programs: [], // Add this line
                social_media: ['']
            };
            this.errors = {};
        },
        
        addSocialMediaInput() {
            this.relawan.social_media.push('');
        },
        
        removeSocialMediaInput(index) {
            this.relawan.social_media.splice(index, 1);
        },
        
        async viewRelawan(id) {
            try {
                this.loading = true;
                this.currentRelawanId = id;
                
                const response = await fetch(`/relawan/${id}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                this.relawan = data.relawan;
                this.showViewDialog = true;
            } catch (error) {
                console.error('Error fetching relawan:', error);
            } finally {
                this.loading = false;
            }
        },
        
        async editRelawan(id) {
            try {
                this.loading = true;
                this.currentRelawanId = id;
                this.dialogMode = 'edit';
                
                const response = await fetch(`/relawan/${id}/edit`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                this.relawan = data.relawan;
                
                // Ensure programs is an array
                if (!this.relawan.programs) {
                    this.relawan.programs = [];
                }
                
                // Ensure program_ids is updated from programs
                this.relawan.program_ids = this.relawan.programs.map(p => p.id);
                
                // Ensure social_media is an array with at least one element
                if (!this.relawan.social_media || !Array.isArray(this.relawan.social_media)) {
                    this.relawan.social_media = [''];
                } else if (this.relawan.social_media.length === 0) {
                    this.relawan.social_media.push('');
                }
                
                this.showDialog = true;
            } catch (error) {
                console.error('Error fetching relawan for edit:', error);
            } finally {
                this.loading = false;
            }
        },
        
        confirmDelete(id) {
            this.currentRelawanId = id;
            this.showDeleteDialog = true;
        },
        
        async deleteRelawan() {
            try {
                this.loading = true;
                
                const response = await fetch(`/relawan/${this.currentRelawanId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    this.showDeleteDialog = false;
                    this.loadPage(this.currentPageUrl);
                } else {
                    alert(result.message || 'Failed to delete volunteer');
                }
            } catch (error) {
                console.error('Error deleting relawan:', error);
                alert('An error occurred while deleting the volunteer');
            } finally {
                this.loading = false;
            }
        },
        
        handleFileChange(event) {
            this.selectedFile = event.target.files[0];
            this.errors.image = null;
            
            // Validate file size
            if (this.selectedFile && this.selectedFile.size > 2 * 1024 * 1024) {
                this.errors.image = 'File size must be less than 2MB';
                event.target.value = '';
                this.selectedFile = null;
                return;
            }
            
            // Show image preview
            if (this.selectedFile) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.$refs.imagePreview.src = e.target.result;
                };
                reader.readAsDataURL(this.selectedFile);
            }
        },
        
        async saveRelawan() {
            try {
                this.loading = true;
                
                const formData = new FormData();
                formData.append('name', this.relawan.name);
                formData.append('email', this.relawan.email || '');
                formData.append('phone', this.relawan.phone || '');
                formData.append('address', this.relawan.address || '');
                formData.append('description', this.relawan.description || '');
                
                // Add program IDs
                if (this.relawan.programs && this.relawan.programs.length > 0) {
                    this.relawan.programs.forEach(program => {
                        formData.append('program_ids[]', program.id);
                    });
                }
                
                // Add social media links
                if (this.relawan.social_media && this.relawan.social_media.length > 0) {
                    this.relawan.social_media.forEach(link => {
                        formData.append('social_media[]', link || '');
                    });
                }
                
                // Add image if selected
                if (this.selectedFile) {
                    formData.append('image', this.selectedFile);
                }
                
                // Add method override for PUT requests
                if (this.dialogMode === 'edit') {
                    formData.append('_method', 'PUT');
                }
                
                const url = this.dialogMode === 'create' 
                    ? '/relawan' 
                    : `/relawan/${this.currentRelawanId}`;
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: formData
                });
                
                const result = await response.json();
                
                if (!response.ok) {
                    if (response.status === 422) {
                        this.errors = result.errors || {};
                    } else {
                        throw new Error(result.message || 'An error occurred');
                    }
                    return;
                }
                
                // Success
                this.closeDialog();
                this.loadPage(this.currentPageUrl); // Reload the current page
                
                // Show success message
                this.$dispatch('show-toast', {
                    message: result.message || 'Relawan saved successfully',
                    type: 'success'
                });
                
            } catch (error) {
                console.error('Error saving relawan:', error);
                this.$dispatch('show-toast', {
                    message: error.message || 'An error occurred while saving',
                    type: 'error'
                });
            } finally {
                this.loading = false;
            }
        },
        
        formatPhoneNumber(input) {
            // Remove all non-numeric characters
            let value = input.replace(/\D/g, '');

            // Handle different prefixes
            if (value.startsWith('0')) {
                value = value.substring(1);
            } else if (value.startsWith('62')) {
                value = value.substring(2);
            }

            // Format as: +62 8xx xxxx xxxx
            if (value.length > 0) {
                let formatted = '+62 ';
                formatted += value.substring(0, 3);
                if (value.length > 3) {
                    formatted += ' ' + value.substring(3, 7);
                }
                if (value.length > 7) {
                    formatted += ' ' + value.substring(7);
                }
                return formatted;
            } else {
                return '';
            }
        },

        formatPhoneForDisplay(phone) {
            if (!phone) return '-';

            // Remove all non-numeric characters and '+' sign
            let number = phone.replace(/[^0-9]/g, '');

            // If it starts with '62', remove it as we'll add it back
            if (number.startsWith('62')) {
                number = number.substring(2);
            }

            // Format as: +62 8xx xxxx xxxx
            return '+62 ' + number.substring(0, 3) + ' ' +
                   number.substring(3, 7) + ' ' +
                   number.substring(7);
        },
        
        loadCurrentPage() {
            this.loadPage(this.currentPageUrl);
        },
        
        formatDateIndonesian(dateString) {
            if (!dateString) return '';
            
            const months = [
                'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
            ];
            
            const date = new Date(dateString);
            const day = date.getDate();
            const month = months[date.getMonth()];
            const year = date.getFullYear();
            
            return `${day} ${month} ${year}`;
        },
        searchPrograms() {
            if (this.programSearchTerm.length < 2) {
                this.showProgramResults = false;
                return;
            }
            
            // Clear previous timeout
            if (this.programSearchTimeout) clearTimeout(this.programSearchTimeout);
            
            // Set a new timeout (300ms debounce)
            this.programSearchTimeout = setTimeout(async () => {
                this.programLoading = true;
                try {
                    const response = await fetch(`/programs/search?q=${encodeURIComponent(this.programSearchTerm)}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        credentials: 'same-origin'
                    });
                    
                    if (response.ok) {
                        this.filteredPrograms = await response.json();
                        this.showProgramResults = this.filteredPrograms.length > 0;
                    } else {
                        console.error('Error searching programs:', response.status);
                        this.filteredPrograms = [];
                        this.showProgramResults = false;
                    }
                } catch (error) {
                    console.error('Error searching programs:', error);
                    this.filteredPrograms = [];
                    this.showProgramResults = false;
                } finally {
                    this.programLoading = false;
                }
            }, 300);
        },
        addProgram(program) {
            // Check if program is already selected
            const exists = this.relawan.programs.some(p => p.id === program.id);
            if (!exists) {
                this.relawan.programs.push(program);
                this.relawan.program_ids = this.relawan.programs.map(p => p.id);
            }
            this.programSearchTerm = '';
            this.showProgramResults = false;
        },
        removeProgram(index) {
            this.relawan.programs.splice(index, 1);
            this.relawan.program_ids = this.relawan.programs.map(p => p.id);
        }
    };
}
</script>
@endsection
