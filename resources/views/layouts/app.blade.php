
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="view-transition" content="same-origin">
    <title>@yield('title') - Berbagi</title>

    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="bg-gray-100" x-data="{
    sidebarOpen: false,
    init() {
        window.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.sidebarOpen = false;
                window.dispatchEvent(new CustomEvent('close-all-dialogs'));
            }
        });
    }
}">
    <!-- Fixed Header -->
    <header class="fixed top-0 right-0 left-0 h-16 bg-white shadow-sm z-40">
        <div class="max-w-full px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-[auto_1fr] lg:grid-cols-[256px_1fr_auto] items-center h-16 gap-4">
                <!-- Logo Section (hidden on mobile) -->
                <div class="hidden lg:flex items-center">
                    <img src="{{ asset('images/berbagi-crop.png') }}" alt="Logo" class="h-8 w-8">
                    <span class="ml-2 text-xl font-semibold">Berbagi</span>
                </div>

                <!-- Mobile hamburger button and Logo -->
                <div class="lg:hidden flex items-center gap-4">
                    <button @click="sidebarOpen = true"
                            class="grid place-items-center w-10 h-10 text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 rounded-md">
                        <x-icon name="menu" width="18" height="18" />
                    </button>
                    <div class="flex items-center">
                        <img src="{{ asset('images/berbagi-crop.png') }}" alt="Logo" class="h-8 w-8">
                        <span class="ml-2 text-xl font-semibold">Berbagi</span>
                    </div>
                </div>

                <!-- Spacer for desktop -->
                <div class="hidden lg:block"></div>

                <!-- User info and logout dropdown -->
                <div class="flex items-center justify-self-end" x-data="profileDropdown">
                    <button @click="open = !open"
                            @click.away="open = false"
                            class="flex items-center text-gray-700 hover:text-gray-900 text-sm">
                        <span>{{ Auth::user()->name }}</span>
                        <x-icon name="chevron-down" width="16" height="16" class="ml-1" />
                    </button>
                    <div x-show="open"
                         x-transition:enter="transition ease-out duration-100"
                         x-transition:enter-start="transform opacity-0 scale-95"
                         x-transition:enter-end="transform opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="transform opacity-100 scale-100"
                         x-transition:leave-end="transform opacity-0 scale-95"
                         class="absolute right-4 top-12 w-32 py-1 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5"
                         x-cloak>
                        <button @click="openProfileDialog()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</button>
                        <a href="/logout" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                    </div>

                    <!-- Profile Dialog -->
                    <div x-show="showProfileDialog"
                         class="fixed inset-0 z-50 overflow-y-auto"
                         style="display: none;"
                         @keydown.escape.window="showProfileDialog = false">
                        <!-- Backdrop -->
                        <div x-show="showProfileDialog"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0"
                             x-transition:enter-end="opacity-100"
                             x-transition:leave="transition ease-in duration-200"
                             x-transition:leave-start="opacity-100"
                             x-transition:leave-end="opacity-0"
                             class="fixed inset-0 bg-gray-500/30 backdrop-blur-xs"
                             @click="showProfileDialog = false">
                        </div>

                        <!-- Dialog Content -->
                        <div class="flex items-start justify-center min-h-screen px-4 py-10 sm:py-16">
                            <div x-show="showProfileDialog"
                                 x-transition:enter="transition ease-out duration-300"
                                 x-transition:enter-start="opacity-0 translate-y-4"
                                 x-transition:enter-end="opacity-100 translate-y-0"
                                 x-transition:leave="transition ease-in duration-200"
                                 x-transition:leave-start="opacity-100 translate-y-0"
                                 x-transition:leave-end="opacity-0 translate-y-4"
                                 class="relative bg-white rounded-lg max-w-lg w-full shadow-xl">
                                <div class="p-4 sm:p-6">
                                    <div class="flex justify-between items-center mb-4">
                                        <h3 class="text-xl font-semibold text-gray-900">Edit Profile</h3>
                                        <button @click="showProfileDialog = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                            <x-icon name="x" width="24" height="24" />
                                        </button>
                                    </div>

                                    <form @submit.prevent="validateAndUpdateProfile" class="space-y-4">
                                        <div class="space-y-4">
                                            <!-- Name -->
                                            <div>
                                                <label for="profile-name" class="block text-sm text-gray-500 mb-1">Name</label>
                                                <input type="text" id="profile-name" x-model="profileData.name"
                                                       :class="{'border-red-300 focus:ring-red-500': errors.name}"
                                                       class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150">
                                                <div x-show="errors.name" class="mt-1 text-sm text-red-500" x-text="errors.name"></div>
                                            </div>

                                            <!-- Email (read-only) -->
                                            <div>
                                                <label for="profile-email" class="block text-sm text-gray-500 mb-1">Email</label>
                                                <input type="email" id="profile-email" x-model="profileData.email" readonly
                                                       class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 bg-gray-50 shadow-sm disabled:text-gray-500 disabled:cursor-not-allowed">
                                            </div>

                                            <!-- Phone -->
                                            <div>
                                                <label for="profile-phone" class="block text-sm text-gray-500 mb-1">Phone</label>
                                                <x-phone-input
                                                    id="profile-phone"
                                                    name="phone"
                                                    x-model="profileData.phone"
                                                    placeholder="+62 8xx xxxx xxxx"
                                                    class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                                                    x-bind:class="errors.phone ? 'border-red-300 focus:ring-red-500' : ''"
                                                />
                                                <div x-show="errors.phone" class="mt-1 text-sm text-red-500" x-text="errors.phone"></div>
                                            </div>

                                            <!-- Manager (only for staff) -->
                                            <div x-show="profileData.role === 'staff'">
                                                <label for="profile-manager" class="block text-sm text-gray-500 mb-1">Manager</label>
                                                <div class="relative">
                                                    <select id="profile-manager" x-model="profileData.manager"
                                                            :class="{'border-red-300 focus:ring-red-500': errors.manager}"
                                                            class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150 appearance-none">
                                                        <option value="">Select Manager</option>
                                                        <option value="Man 1">Man 1</option>
                                                        <option value="Man 2">Man 2</option>
                                                        <option value="Man 3">Man 3</option>
                                                        <option value="Man 4">Man 4</option>
                                                    </select>
                                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                                                        <x-icon name="chevron-down" width="16" height="16" />
                                                    </div>
                                                </div>
                                                <div x-show="errors.manager" class="mt-1 text-sm text-red-500" x-text="errors.manager"></div>
                                            </div>

                                            <!-- Role (read-only) -->
                                            <div>
                                                <label for="profile-role" class="block text-sm text-gray-500 mb-1">Role</label>
                                                <input type="text" id="profile-role" x-model="profileData.role" readonly
                                                       class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 bg-gray-50 shadow-sm disabled:text-gray-500 disabled:cursor-not-allowed capitalize">
                                            </div>
                                        </div>

                                        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                                            <button type="submit"
                                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150">
                                                Save
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="flex min-h-screen pt-16">
        <!-- Mobile sidebar backdrop -->
        <div x-show="sidebarOpen"
             x-transition:enter="transition-opacity ease-linear duration-200"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-20 bg-gray-500/30 lg:hidden backdrop-blur-xs"
             @click="sidebarOpen = false"
             x-cloak></div>

        <!-- Mobile sidebar -->
        <div x-show="sidebarOpen"
             x-transition:enter="transition ease-out duration-200 transform"
             x-transition:enter-start="-translate-x-full"
             x-transition:enter-end="translate-x-0"
             x-transition:leave="transition ease-in duration-200 transform"
             x-transition:leave-start="translate-x-0"
             x-transition:leave-end="-translate-x-full"
             class="fixed inset-y-0 left-0 z-30 w-64 bg-white lg:hidden pt-16"
             @click.away="sidebarOpen = false"
             x-cloak>
            @include('layouts.partials.navigation')
        </div>

        <!-- Desktop sidebar -->
        <div class="hidden lg:block fixed top-16 w-64 h-[calc(100vh-4rem)] bg-white shadow-sm overflow-y-auto">
            @include('layouts.partials.navigation')
        </div>

        <!-- Main Content Area -->
        <main class="flex-1 w-full lg:ml-64 lg:max-w-[calc(100vw-280px)]">
            <div class="py-6">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6 text-gray-900">
                            @yield('content')
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    @stack('scripts')

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('profileDropdown', () => ({
                open: false,
                showProfileDialog: false,
                profileData: {},
                errors: {},

                openProfileDialog() {
                    this.open = false;
                    this.fetchProfileData();
                },

                async fetchProfileData() {
                    try {
                        const response = await fetch('/profile');
                        if (!response.ok) throw new Error('Failed to fetch profile data');

                        this.profileData = await response.json();
                        this.errors = {};
                        this.showProfileDialog = true;
                    } catch (error) {
                        console.error('Error fetching profile:', error);
                        alert('Failed to load profile data');
                    }
                },

                validateProfile() {
                    this.errors = {};

                    // Name validation
                    if (!this.profileData.name?.trim()) {
                        this.errors.name = 'Name is required';
                    }

                    // Phone validation - only check format if provided
                    if (this.profileData.phone && this.profileData.phone.trim()) {
                        // The phone input component should always format it correctly,
                        // but just in case, check if it starts with +62
                        if (!this.profileData.phone.trim().startsWith('+62')) {
                            this.errors.phone = 'Phone number must start with +62';
                        }
                    }

                    // Manager validation (required if role is staff)
                    if (this.profileData.role === 'staff' && !this.profileData.manager) {
                        this.errors.manager = 'Manager is required for staff role';
                    }

                    return Object.keys(this.errors).length === 0;
                },

                validateAndUpdateProfile() {
                    if (this.validateProfile()) {
                        this.updateProfile();
                    }
                },

                async updateProfile() {
                    try {
                        // Create form data to handle the PUT request properly
                        const formData = new FormData();
                        formData.append('_method', 'PUT'); // Laravel method spoofing
                        formData.append('name', this.profileData.name);
                        formData.append('phone', this.profileData.phone || '');
                        if (this.profileData.manager) {
                            formData.append('manager', this.profileData.manager);
                        }

                        const response = await fetch('/profile', {
                            method: 'POST', // Use POST with _method=PUT for method spoofing
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: formData
                        });

                        const data = await response.json();

                        if (!response.ok) {
                            this.errors = data.errors || {};
                            return;
                        }

                        this.showProfileDialog = false;
                        this.errors = {};

                        // Update the displayed name in the header
                        if (data.user && data.user.name) {
                            document.querySelector('.flex.items-center.text-gray-700.hover\\:text-gray-900.text-sm span').textContent = data.user.name;
                        }

                        // Remove the alert
                        // alert('Profile updated successfully');
                    } catch (error) {
                        alert('Failed to update profile');
                    }
                }
            }));
        });
    </script>
</body>

</html>
