@props(['phone' => null])

@if($phone)
    @php
        // Remove all non-numeric characters and '+' sign
        $number = preg_replace('/[^0-9]/', '', $phone);
        
        // If it starts with '62', remove it as we'll add it back
        if (str_starts_with($number, '62')) {
            $number = substr($number, 2);
        }
        
        // Format as: +62 8xx xxxx xxxx
        $formattedPhone = '+62 ' . substr($number, 0, 3) . ' ' .
            substr($number, 3, 4) . ' ' .
            substr($number, 7);
    @endphp
    {{ $formattedPhone }}
@else
    -
@endif