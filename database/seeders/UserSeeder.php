<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password'),
            'manager' => 'Man 1',
            'phone' => '+62 812 3456 7890',
        ]);

        // Create admin user: bobby
        User::factory()->create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('password'),
            'manager' => 'Man 2',
            'phone' => '+62 812 3456 7891',
        ]);

        // Create staff user: darkflintz
        User::factory()->create([
            'name' => 'Darkflintz',
            'email' => '<EMAIL>',
            'role' => 'staff',
            'password' => bcrypt('password'),
            'manager' => 'Man 1',
            'phone' => '+62 812 3456 7892',
        ]);

        // Create 15 staff users with random managers
        $managers = ['Man 1', 'Man 2', 'Man 3', 'Man 4'];

        for ($i = 0; $i < 15; $i++) {
            User::factory()->create([
                'role' => 'staff',
                'manager' => $managers[array_rand($managers)],
                'phone' => '+62' . rand(812, 854) . rand(1000, 9999) . rand(1000, 9999),
            ]);
        }

        // Create regular users (90 users)
        User::factory(90)->create([
            'role' => 'user',
            'phone' => '+62' . rand(812, 854) . rand(1000, 9999) . rand(1000, 9999),
        ]);

        // Create banned users
        User::factory(5)->create([
            'role' => 'banned',
        ]);
    }
}
