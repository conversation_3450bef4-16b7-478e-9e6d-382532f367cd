<?php

namespace Database\Seeders;

use App\Models\Donatur;
use App\Models\User;
use Illuminate\Database\Seeder;

class DonaturSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get staff users to associate with donaturs
        $staffUsers = User::whereIn('role', ['admin', 'staff'])->get();

        // Create 25 donaturs
        for ($i = 0; $i < 25; $i++) {
            $socialMedia = [];

            // Randomly add 0-3 social media links
            $socialMediaCount = rand(0, 3);
            for ($j = 0; $j < $socialMediaCount; $j++) {
                $platform = ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com'][rand(0, 3)];
                $socialMedia[] = "https://{$platform}/user" . rand(1000, 9999);
            }

            Donatur::create([
                'name' => fake()->name(),
                'email' => fake()->optional(0.8)->safeEmail(),
                'phone' => '+62' . rand(812, 854) . rand(1000, 9999) . rand(1000, 9999),
                'address' => fake()->optional(0.6)->address(),
                'description' => fake()->optional(0.5)->paragraph(),
                'social_media' => $socialMedia,
                'user_id' => $staffUsers->isNotEmpty() ? $staffUsers->random()->id : null,
            ]);
        }
    }
}
