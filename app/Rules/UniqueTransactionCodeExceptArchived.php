<?php

namespace App\Rules;

use App\Models\Program;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueTransactionCodeExceptArchived implements ValidationRule
{
    /**
     * The ID of the program being updated.
     *
     * @var string|null
     */
    protected $programId;

    /**
     * Create a new rule instance.
     *
     * @param  string|null  $programId
     * @return void
     */
    public function __construct(?string $programId = null)
    {
        $this->programId = $programId;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Build the query to check for duplicate transaction codes
        $query = Program::where('transaction_code', $value)
            ->where('status', '!=', 'archive'); // Exclude archived programs

        // If we're updating an existing program, exclude it from the check
        if ($this->programId) {
            $query->where('id', '!=', $this->programId);
        }

        // If a program with this transaction code exists (and is not archived), fail validation
        if ($query->exists()) {
            $fail('The transaction code has already been taken by another active program. You can only reuse transaction codes from archived programs.');
        }
    }
}
